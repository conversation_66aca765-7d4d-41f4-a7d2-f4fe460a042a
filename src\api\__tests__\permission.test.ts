import { consumePermission } from '../auth';
import { httpClient } from '../http-client';

// Mock httpClient
jest.mock('../http-client', () => ({
  httpClient: {
    post: jest.fn(),
  },
}));

const mockHttpClient = httpClient as jest.Mocked<typeof httpClient>;

describe('Permission API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('consumePermission', () => {
    it('should call the correct endpoint with default parameters', async () => {
      const mockResponse = { success: true, remaining_credits: 10 };
      mockHttpClient.post.mockResolvedValue(mockResponse);

      const result = await consumePermission();

      expect(mockHttpClient.post).toHaveBeenCalledWith(
        '/api/v1/permission/consume',
        {}
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call the endpoint with custom parameters', async () => {
      const mockResponse = { success: true, remaining_credits: 9 };
      mockHttpClient.post.mockResolvedValue(mockResponse);

      const request = {
        consumed_num: 1,
        description: 'Remove BG'
      };

      const result = await consumePermission(request);

      expect(mockHttpClient.post).toHaveBeenCalledWith(
        '/api/v1/permission/consume',
        request
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle batch processing description', async () => {
      const mockResponse = { success: true, remaining_credits: 8 };
      mockHttpClient.post.mockResolvedValue(mockResponse);

      const request = {
        consumed_num: 1,
        description: 'Batch Editor RemoveBG'
      };

      const result = await consumePermission(request);

      expect(mockHttpClient.post).toHaveBeenCalledWith(
        '/api/v1/permission/consume',
        request
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors gracefully', async () => {
      const mockError = new Error('Network error');
      mockHttpClient.post.mockRejectedValue(mockError);

      const result = await consumePermission({
        consumed_num: 1,
        description: 'Remove BG'
      });

      expect(result).toEqual({
        success: false,
        message: 'Network error'
      });
    });

    it('should handle unknown errors gracefully', async () => {
      mockHttpClient.post.mockRejectedValue('Unknown error');

      const result = await consumePermission();

      expect(result).toEqual({
        success: false,
        message: '权益消耗失败'
      });
    });
  });
});

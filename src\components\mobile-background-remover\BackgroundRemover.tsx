'use client';

import { But<PERSON> } from '@/components/ui/Button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/Dialog';
import { Input } from '@/components/ui/Input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/Popover';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/Tooltip';
import { removeBackground, consumePermission } from '@/api';
import {
  clearHistory,
  redo,
  undo,
  useImageStore,
  type ImageState,
} from '@/store/imageStore';
import { useStore } from 'zustand';
import { imageStorage } from '@/storage/indexeddbStorage';
import { useTips } from '@/components/ui/Tips';
import {
  MAX_SINGLE_IMAGES_LIMIT,
  ERASE_HISTORY_LIMIT,
} from '@/config/constants';
import {
  validateImageFormat,
  createImageFromSource,
  cleanupImageDeletionState,
} from '@/lib/imageUtils/imageState';

import { Plus } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDropzone, type FileRejection } from 'react-dropzone';
import { MobileBackgroundColorPicker } from './BackgroundColorPicker';
import { MobileBackgroundImagePicker } from './BackgroundImagePicker';
import {
  MobileCanvasImageEditor,
  type MobileCanvasImageEditorHandles,
} from './CanvasImageEditor';
import { MbileEraserTool } from './EraserTool';
import { MobileBackgroundBlurPicker } from './BackgroundBlurPicker';
import StarryNightLoading from '../starry-night-loading';
import { useImageStorage } from '@/hooks/background-remover/useImageStorage';
import { formatImageDimensions } from '@/lib/imageUtils/imageResize';
import { useTranslations } from 'next-intl';
import { ImageHistoryBar } from './ImageHistoryBar';
import { SAMPLE_IMAGES } from '@/config/constants';

/**
 * 背景移除组件的头部。
 * 包含标题、历史记录管理、背景颜色选择器和背景模糊选择器。
 */

/**
 * 主要的背景移除组件，包含图片上传、编辑区域和历史记录管理。
 */
export function MobileBackgroundRemover() {
  // 国际化翻译
  const common = useTranslations('common');
  const singleImage = useTranslations('singleImage');
  const messages = useTranslations('messages');
  const mobile = useTranslations('mobile');

  // 从 Zustand store 获取状态
  // 使用自定义背景图片存储Hook
  const {
    uploadedBackgroundImages,
    addBackgroundImage,
    deleteBackgroundImage,
  } = useImageStorage();

  // 订阅原始的 Map 和 Set 对象，避免无限重渲染
  const imagesMap = useImageStore(s => s.images);
  const selectedImageIds = useImageStore(s => s.selectedImageIds);
  const temporalState = useStore(useImageStore.temporal, state => state);
  const imageActions = useImageStore.getState();
  const { showTips } = useTips();

  // 在组件内部将 Map 转换为数组，以用于渲染
  // 按照图片的时间戳排序，确保最新上传的图片在最前面
  const images = Array.from(imagesMap.values()).sort((a, b) => {
    // 使用图片的timestamp字段进行排序
    const aTime = a.timestamp || 0;
    const bTime = b.timestamp || 0;

    // 按时间戳降序排序（最新的在前）
    if (aTime !== bTime) {
      return bTime - aTime;
    }

    // 如果时间戳相同，按照图片名称排序
    return a.name.localeCompare(b.name);
  });

  // 当前正在编辑的图片 ID (取第一个选中的)
  const currentImageId = selectedImageIds.values().next().value || null;

  // 当前图片的 Image 对象
  const [currentImageObject, setCurrentImageObject] =
    useState<HTMLImageElement | null>(null);

  // 是否处于对比模式（显示原图）
  const [isCompareActive, setIsCompareActive] = useState(false);
  // 控制橡皮擦弹窗强制关闭
  const [forceCloseEraserTool, setForceCloseEraserTool] = useState(false);
  // 当前缩放比例
  const [currentScale, setCurrentScale] = useState(1);
  // 图片加载后的初始缩放比例
  // const [initialScale, setInitialScale] = useState(1);
  // 对 Canvas 编辑器组件的引用
  const canvasEditorRef = useRef<MobileCanvasImageEditorHandles>(null);
  // API 是否正在处理中
  const [isLoadingApi, setIsLoadingApi] = useState<boolean>(false);
  // URL输入弹窗的状态
  const [isUrlDialogOpen, setIsUrlDialogOpen] = useState(false);
  const [inputUrl, setInputUrl] = useState('');
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);

  // Canvas 容器的动态样式
  const [canvasWrapperStyle, setCanvasWrapperStyle] =
    useState<React.CSSProperties>({});
  // Canvas 容器的尺寸
  const [wrapperSize, setWrapperSize] = useState({ width: 0, height: 0 });

  // 初始化移动端开发工具
  const mobileDevToolsInit = useCallback(() => {
    // 仅在客户端执行
    if (typeof window === 'undefined') return;

    // 创建script元素
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/eruda';
    script.onload = function () {
      // 加载完成后初始化
      if ((window as Window).eruda) {
        (window as Window).eruda?.init();
        console.log('Eruda initialized via CDN');
      }
    };
    document.body.appendChild(script);
  }, []);

  // 销毁移动端开发工具
  const mobileDevToolsDestroy = useCallback(() => {
    // 仅在客户端执行
    if (typeof window === 'undefined') return;

    // 使用eruda的官方destroy方法
    try {
      if (
        (window as Window).eruda &&
        typeof (window as Window).eruda?.destroy === 'function'
      ) {
        (window as Window).eruda?.destroy();
        console.log('Eruda destroyed');
      }
    } catch (error) {
      console.warn('Failed to destroy eruda:', error);
    }
  }, []);

  useEffect(() => {
    mobileDevToolsInit();

    // 清理函数
    return () => {
      mobileDevToolsDestroy();
    };
  }, [mobileDevToolsInit, mobileDevToolsDestroy]);

  // 在文件顶部添加这个接口定义
  interface Window {
    eruda?: {
      init: () => void;
      destroy: () => void;
    };
  }

  // 当图片列表为空时，确保重置UI状态
  useEffect(() => {
    if (imagesMap.size === 0) {
      setCurrentImageObject(null);
      setCanvasWrapperStyle({});
      setWrapperSize({ width: 0, height: 0 });
      // setInitialScale(1);
      setCurrentScale(1);
    }
  }, [imagesMap]);

  // 当 currentImageId 变化时，加载对应的图片对象并计算合适的初始缩放和容器尺寸
  useEffect(() => {
    let isCancelled = false;

    if (!currentImageId) {
      setCurrentImageObject(null);
      setCanvasWrapperStyle({});
      setWrapperSize({ width: 0, height: 0 });
      // setInitialScale(1);
      setCurrentScale(1);
      return;
    }

    const imageToLoad = useImageStore.getState().images.get(currentImageId);

    if (imageToLoad) {
      const img = new window.Image();
      img.onload = () => {
        if (isCancelled) return;
        const imgNaturalWidth = img.naturalWidth;
        const imgNaturalHeight = img.naturalHeight;
        if (
          imgNaturalWidth === 0 ||
          imgNaturalHeight === 0 ||
          window.innerHeight === 0 ||
          window.innerWidth === 0
        ) {
          setCanvasWrapperStyle({});
          setWrapperSize({ width: 0, height: 0 });
          // setInitialScale(1);
          setCurrentScale(1);
          setCurrentImageObject(img);
          return;
        }

        // 计算 canvas 的最佳显示尺寸，适应移动端屏幕
        const aspectRatio = imgNaturalWidth / imgNaturalHeight;

        // 计算可用的最大宽度（屏幕宽度减去左右padding 32px）
        const maxAvailableWidth = Math.max(50, window.innerWidth - 32);

        // 计算可用的最大高度
        const maxAvailableHeight = Math.max(50, window.innerHeight - 488);

        // 设置最小显示尺寸（确保小图能充分利用屏幕）
        const minDisplayWidth = maxAvailableWidth;
        const minDisplayHeight = 200;

        // 计算不同约束下的缩放比例
        const scaleByWidth = maxAvailableWidth / imgNaturalWidth;
        const scaleByHeight = maxAvailableHeight / imgNaturalHeight;
        const scaleByMinWidth = minDisplayWidth / imgNaturalWidth;

        // 选择合适的缩放比例
        let finalScale: number;

        // 如果图片很小，需要放大到最小宽度
        if (imgNaturalWidth < minDisplayWidth) {
          // 检查按最小宽度放大后，高度是否超出
          const heightAfterMinWidthScale = imgNaturalHeight * scaleByMinWidth;
          if (heightAfterMinWidthScale <= maxAvailableHeight) {
            // 高度不超出，使用最小宽度缩放
            finalScale = scaleByMinWidth;
          } else {
            // 高度超出，使用高度约束（确保图片完全显示）
            finalScale = scaleByHeight;
          }
        } else {
          // 图片不小，选择更严格的约束（确保超宽、超高图片都能完全显示）
          finalScale = Math.min(scaleByWidth, scaleByHeight);
        }

        // 计算最终尺寸
        const targetCanvasWidth = Math.max(
          minDisplayHeight * aspectRatio, // 确保不会太小
          imgNaturalWidth * finalScale
        );
        const targetCanvasHeight = Math.max(
          minDisplayHeight,
          imgNaturalHeight * finalScale
        );

        // 最终确保不超过最大限制，并确保是整数
        const finalCanvasWidth = Math.round(
          Math.min(targetCanvasWidth, maxAvailableWidth)
        );
        const finalCanvasHeight = Math.round(
          Math.min(targetCanvasHeight, maxAvailableHeight)
        );

        const newInitialScale = finalCanvasWidth / imgNaturalWidth;

        setCanvasWrapperStyle({
          width: `100%`,
          height: maxAvailableHeight,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        });
        setWrapperSize({
          width: finalCanvasWidth,
          height: finalCanvasHeight,
        });
        // setInitialScale(newInitialScale);
        setCurrentScale(newInitialScale);
        setCurrentImageObject(img);
      };
      img.onerror = () => {
        if (isCancelled) return;
        console.error('从历史记录加载图片对象失败。');
        setCurrentImageObject(null);
      };
      img.src = imageToLoad.previewUrl; // 使用 store 中的 previewUrl
    }

    return () => {
      isCancelled = true;
    };
  }, [currentImageId]);

  // 从 imageHistory 中获取当前图片的状态
  const currentImageFromHistory = currentImageId
    ? useImageStore.getState().images.get(currentImageId)
    : undefined;

  // 监听当前图片变化，恢复橡皮擦画布状态
  useEffect(() => {
    if (!canvasEditorRef.current || !currentImageObject) {
      return;
    }

    // 延迟恢复，确保canvas已经初始化
    const timeoutId = setTimeout(() => {
      if (canvasEditorRef.current) {
        if (currentImageFromHistory?.currentEraseCanvasData) {
          canvasEditorRef.current.restoreEraseCanvasData(
            currentImageFromHistory.currentEraseCanvasData
          );
        } else {
          // 如果没有橡皮擦数据，清空橡皮擦图层
          canvasEditorRef.current.clearEraseLayer();
        }
      }
    }, 150);

    return () => clearTimeout(timeoutId);
  }, [
    currentImageFromHistory?.currentEraseCanvasData,
    currentImageFromHistory?.eraseOperationCount,
    currentImageId,
    currentImageObject,
  ]);

  const processedUrl = currentImageFromHistory?.processedUrl;
  const selectedBackgroundColor =
    currentImageFromHistory?.backgroundColor ?? 'transparent';
  const backgroundImageUrl = currentImageFromHistory?.backgroundImageUrl;
  const isBlurEnabled = currentImageFromHistory?.isBlurEnabled ?? false;
  const blurAmount = currentImageFromHistory?.blurAmount ?? 0;

  // 橡皮擦相关状态
  const isEraseMode = currentImageFromHistory?.isEraseMode ?? false;
  const isRestoreMode = currentImageFromHistory?.isRestoreMode ?? false;
  const eraseBrushSize = currentImageFromHistory?.eraseBrushSize ?? 20;

  // 组件内部的精细化撤销重做暂时注释，使用全局的
  // const historyIndex = currentImageFromHistory?.actionHistoryIndex ?? -1;
  // const history = currentImageFromHistory?.actionHistory ?? [];

  /**
   * 更新当前图片的设置，并记录到操作历史中。
   * @param newSettings - 新的设置项。
   */
  const updateCurrentImageSettings = useCallback(
    (newSettings: Partial<Omit<ImageState, 'id' | 'file'>>) => {
      if (!currentImageId) return;

      // 如果激活了橡皮擦模式且当前图片有背景去除错误，则清除错误状态
      if (newSettings.isEraseMode || newSettings.isRestoreMode) {
        const currentImage = useImageStore
          .getState()
          .images.get(currentImageId);
        if (currentImage?.status === 'bg-remove-failed') {
          newSettings = {
            ...newSettings,
            status: 'original',
          };
        }
      }

      // 直接调用 store action 更新
      imageActions.updateImage(currentImageId, newSettings);
    },
    [currentImageId, imageActions]
  );

  /**
   * 撤销操作。
   */
  const handleUndo = useCallback(() => {
    const temporalState = useImageStore.temporal.getState();

    // 检查撤销后是否会导致图片列表为空
    if (temporalState.pastStates.length > 0) {
      const lastPastState =
        temporalState.pastStates[temporalState.pastStates.length - 1];

      // 如果撤销后图片列表为空，则不执行撤销
      if (lastPastState?.images?.size === 0) {
        return;
      }
    }

    undo();
  }, []);

  /**
   * 重做操作。
   */
  const handleRedo = redo; // 直接使用从 store 导出的 redo

  /**
   * 处理橡皮擦操作完成
   */
  const handleEraseOperationComplete = useCallback(
    (data: {
      historyData: ImageData | null;
      currentCanvasData: ImageData | null;
    }) => {
      if (!currentImageId) return;

      const currentImage = useImageStore.getState().images.get(currentImageId);
      if (!currentImage) return;

      // 准备更新的数据
      const updates: Partial<Omit<ImageState, 'id'>> = {
        currentEraseCanvasData: data.currentCanvasData,
        eraseOperationCount: currentImage.eraseOperationCount + 1,
      };

      // 总是更新橡皮擦历史记录，即使是第一次操作（空画布）
      // 这确保每次橡皮擦操作都会创建全局历史记录，使撤销功能正常工作
      if (data.historyData) {
        const newEraseHistory = [
          ...currentImage.eraseHistory,
          data.historyData,
        ];

        // 限制历史记录数量，防止内存过度使用
        if (newEraseHistory.length > ERASE_HISTORY_LIMIT) {
          newEraseHistory.splice(
            0,
            newEraseHistory.length - ERASE_HISTORY_LIMIT
          );
        }

        updates.eraseHistory = newEraseHistory;
      }

      // 一次性更新所有橡皮擦相关状态，总是创建一条全局历史记录
      imageActions.updateImage(currentImageId, updates);
    },
    [currentImageId, imageActions]
  );

  // 跟踪正在处理的图片ID，用于处理删除时的状态清理
  const [processingImageIds, setProcessingImageIds] = useState<Set<string>>(
    new Set()
  );

  /**
   * 调用 API 移除图片背景。
   * @param imageDataUrl - 图片的 Data URL。
   * @returns 返回包含处理后图像的 Blob URL，或在失败时返回 null。
   */
  const handleRemoveBackground = useCallback(
    async (imageDataUrl: string, imageId?: string) => {
      setIsLoadingApi(true);

      // 如果提供了imageId，将其添加到正在处理的列表中
      if (imageId) {
        setProcessingImageIds(prev => new Set(prev).add(imageId));
      }

      try {
        const imageBlob = await removeBackground({ image: imageDataUrl });
        const objectURL = URL.createObjectURL(imageBlob);

        // 调用权益消耗接口
        try {
          await consumePermission({
            consumed_num: 1,
            description: 'Remove BG',
          });
          console.log('移动端权益消耗成功');
        } catch (consumeError) {
          console.error('移动端权益消耗失败:', consumeError);
          // 权益消耗失败不影响主要功能，只记录错误
        }

        return objectURL;
      } catch (error) {
        console.error('移除背景失败:', error);
        return null;
      } finally {
        // 清理状态
        setIsLoadingApi(false);
        if (imageId) {
          setProcessingImageIds(prev => {
            const newSet = new Set(prev);
            newSet.delete(imageId);
            return newSet;
          });
        }
      }
    },
    []
  );

  /**
   * 通用的自动背景去除函数
   */
  const processImageBackgroundRemoval = useCallback(
    async (
      image: {
        id: string;
        file?: File;
        previewUrl: string;
        status: string;
        processedUrl?: string | null;
      },
      handleRemoveBackground: (
        imageDataUrl: string,
        imageId?: string
      ) => Promise<string | null>
    ): Promise<boolean> => {
      if (image.status !== 'original' || image.processedUrl) {
        return false; // 不需要处理
      }

      try {
        console.log(`检测到 original 状态图片，开始自动去除背景: ${image.id}`);

        // 将文件转换为 Data URL
        let imageDataUrl: string;
        if (image.file) {
          imageDataUrl = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(image.file!);
          });
        } else {
          imageDataUrl = image.previewUrl;
        }

        // 调用背景去除API
        const resultUrl = await handleRemoveBackground(imageDataUrl, image.id);

        if (resultUrl) {
          // 更新 store
          useImageStore.getState().updateImage(image.id, {
            processedUrl: resultUrl,
            status: 'bg-removed',
          });
          console.log(`自动背景去除完成: ${image.id}`);
          return true;
        }
      } catch (error) {
        console.error(`自动处理图片背景去除失败: ${image.id}`, error);
      }

      return false;
    },
    []
  );

  /**
   * 处理文件拖放事件。
   */
  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: FileRejection[]) => {
      // 处理被拒绝的文件
      if (rejectedFiles.length > 0) {
        const fileTooLargeFiles = rejectedFiles.filter(rejection =>
          rejection.errors.some(error => error.code === 'file-too-large')
        );
        const invalidFormatFiles = rejectedFiles.filter(rejection =>
          rejection.errors.some(error => error.code === 'invalid-file-type')
        );

        if (fileTooLargeFiles.length > 0) {
          showTips('error', messages('batchEditor.fileSizeMaximum'), 4000);
        } else if (invalidFormatFiles.length > 0) {
          showTips(
            'error',
            messages('singleImage.unsupportedImageFormat'),
            4000
          );
        }
      }

      if (acceptedFiles.length === 0) {
        return;
      }

      // 检查图片数量限制
      const currentImageCount = images.length;
      const newFileCount = acceptedFiles.length;
      const totalCount = currentImageCount + newFileCount;

      // 单次上传图片数量限制
      if (newFileCount > MAX_SINGLE_IMAGES_LIMIT) {
        showTips(
          'error',
          messages('singleImage.imagesExceedLimit', {
            count: MAX_SINGLE_IMAGES_LIMIT,
          }),
          4000
        );
        return;
      }

      // 上传图片总数量限制
      if (totalCount > MAX_SINGLE_IMAGES_LIMIT) {
        showTips(
          'error',
          messages('singleImage.imagesExceedLimit', {
            count: MAX_SINGLE_IMAGES_LIMIT,
          }),
          4000
        );
        return;
      }

      // 双重验证：确保所有接受的文件都符合格式和大小要求
      const validFiles = acceptedFiles.filter(file => {
        const validation = validateImageFormat(file);
        if (!validation.isValid) {
          console.warn(
            `File ${file.name} failed validation despite being accepted by dropzone`
          );
          if (validation.error === 'file-too-large') {
            showTips('error', messages('batchEditor.fileSizeMaximum'), 3000);
          } else {
            showTips(
              'error',
              messages('singleImage.unsupportedImageFormat'),
              3000
            );
          }
        }
        return validation.isValid;
      });

      if (validFiles.length === 0) {
        return;
      }

      setIsLoadingApi(true);

      const processAllFiles = async () => {
        // 暂停历史记录跟踪，因为这是初始化过程
        useImageStore.temporal.getState().pause();

        for (let i = 0; i < validFiles.length; i++) {
          const file = validFiles[i];
          try {
            const newImage = await createImageFromSource(file);
            imageActions.addImage(newImage);

            // 第一张图片立即选中，以便显示上传状态
            if (i === 0) {
              imageActions.clearSelection();
              imageActions.toggleImageSelection(newImage.id);
            }

            // 只处理第一张图片，其他图片保持 original 状态
            if (i === 0) {
              await processImageBackgroundRemoval(
                newImage,
                handleRemoveBackground
              );
            } else {
              // 后续图片保持 original 状态，等待用户点击时处理
              console.log(
                `图片 ${file.name} 保持 original 状态，等待用户点击时处理`
              );
            }
          } catch (error) {
            console.error('Error processing file:', error);
            showTips('error', messages('singleImage.imageUploadFailed'), 3000);
          }
        }

        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingApi(false);
      };

      processAllFiles();
    },
    [
      imageActions,
      handleRemoveBackground,
      processImageBackgroundRemoval,
      images.length,
      messages,
      showTips,
    ]
  );

  /**
   * 从URL加载图片 - 增强版本，支持多种加载方式和错误处理
   */
  const handleLoadFromUrl = useCallback(
    async (url: string) => {
      if (!url.trim()) return;

      setIsLoadingUrl(true);
      setIsLoadingApi(true);

      // 暂停历史记录跟踪，因为这是初始化过程
      useImageStore.temporal.getState().pause();

      try {
        let blob: Blob;

        // 尝试多种方法获取图片
        try {
          // 方法1：直接fetch（适用于同源或支持CORS的图片）
          const response = await fetch(url, {
            mode: 'cors',
            headers: {
              Accept: 'image/*',
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          blob = await response.blob();
        } catch (corsError) {
          console.warn('直接fetch失败，使用服务端代理:', corsError);

          // 方法2：使用我们自己的服务端代理API
          try {
            const proxyResponse = await fetch(
              `/apps/api/proxy-image?url=${encodeURIComponent(url)}`
            );

            if (!proxyResponse.ok) {
              const errorData = await proxyResponse
                .json()
                .catch(() => ({ error: '代理服务器错误' }));
              throw new Error(errorData.error || '代理请求失败');
            }

            blob = await proxyResponse.blob();
          } catch (proxyError) {
            console.warn('服务端代理失败，尝试Canvas方式:', proxyError);

            // 方法3：使用Canvas和图片元素（最后的尝试）
            try {
              const img = new window.Image();
              img.crossOrigin = 'anonymous';

              await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = () => {
                  // 尝试不设置crossOrigin
                  const img2 = new window.Image();
                  img2.onload = resolve;
                  img2.onerror = reject;
                  img2.src = url;
                };
                img.src = url;
              });

              // 创建Canvas并转换为blob
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              if (!ctx) {
                throw new Error('无法创建Canvas上下文');
              }

              canvas.width = img.naturalWidth;
              canvas.height = img.naturalHeight;
              ctx.drawImage(img, 0, 0);

              blob = await new Promise<Blob>((resolve, reject) => {
                canvas.toBlob(result => {
                  if (result) {
                    resolve(result);
                  } else {
                    reject(new Error('Canvas转换失败'));
                  }
                }, 'image/png');
              });
            } catch (canvasError) {
              throw new Error(
                `无法加载图片：CORS限制或图片不存在。详情: ${canvasError}`
              );
            }
          }
        }

        // 验证blob类型
        if (!blob.type.startsWith('image/')) {
          // 尝试从URL推断图片类型
          const ext = url.split('.').pop()?.toLowerCase();
          const mimeTypes: { [key: string]: string } = {
            jpg: 'image/jpeg',
            jpeg: 'image/jpeg',
            png: 'image/png',
            webp: 'image/webp',
            gif: 'image/gif',
          };

          if (ext && mimeTypes[ext]) {
            blob = new Blob([blob], { type: mimeTypes[ext] });
          } else {
            throw new Error('无法识别的图片格式');
          }
        }

        const file = new File([blob], `url_image_${Date.now()}`, {
          type: blob.type,
        });

        const newImage = await createImageFromSource(file);
        imageActions.addImage(newImage);

        // 默认选中新添加的图片
        imageActions.clearSelection();
        imageActions.toggleImageSelection(newImage.id);

        // 关闭弹窗并清空输入
        setIsUrlDialogOpen(false);
        setInputUrl('');

        // 将 Blob 转换为 Data URL 以发送到后端
        const dataUrl = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });

        // 调用 API 处理图片
        const resultUrl = await handleRemoveBackground(dataUrl, newImage.id);
        if (resultUrl) {
          imageActions.updateImage(newImage.id, {
            processedUrl: resultUrl,
            status: 'bg-removed',
          });
        } else {
          // 如果背景去除失败，更新状态
          imageActions.updateImage(newImage.id, {
            status: 'bg-remove-failed',
          });
        }
      } catch (error) {
        console.error('从URL加载图片失败:', error);
        showTips('error', messages('singleImage.unableToOpenImage'));
      } finally {
        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingUrl(false);
        setIsLoadingApi(false);
      }
    },
    [imageActions, handleRemoveBackground, showTips, messages]
  );

  /**
   * 加载示例图片
   * 如果提供了 processedUrl，则会加载本地预处理好的图片，并模拟一个假的加载过程。
   * 否则，调用真实的 API 进行背景移除。
   */
  const handleLoadSampleImage = useCallback(
    async (sampleUrl: string, sampleName: string, processedUrl?: string) => {
      setIsLoadingApi(true);
      try {
        // 获取示例图片数据并创建 File 对象
        const response = await fetch(sampleUrl);
        if (!response.ok) {
          throw new Error(`无法获取示例图片: ${response.statusText}`);
        }
        const blob = await response.blob();
        const file = new File([blob], sampleName, { type: blob.type });

        const newImage = await createImageFromSource(file);
        imageActions.addImage(newImage);

        // 默认选中新添加的图片
        imageActions.clearSelection();
        imageActions.toggleImageSelection(newImage.id);

        let resultUrl: string | null = null;
        // 如果提供了预处理图片的URL，则直接使用它
        if (processedUrl) {
          // 模拟API调用延迟，提供更好的用户体验, 同时展示加载动画
          await new Promise(resolve => setTimeout(resolve, 1500));
          resultUrl = processedUrl;
        } else {
          // 否则，调用真实的API
          const dataUrl = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(file);
          });
          resultUrl = await handleRemoveBackground(dataUrl);
        }

        if (resultUrl) {
          imageActions.updateImage(newImage.id, {
            processedUrl: resultUrl,
            status: 'bg-removed',
          });
        } else {
          // 如果处理失败，可以添加一些用户提示，或者从历史记录中移除该图片
          console.error(`处理示例图片 ${sampleName} 失败。`);
          // 可选: imageActions.removeImage(newImage.id);
        }
      } catch (error) {
        console.error('加载示例图片失败:', error);
      } finally {
        setIsLoadingApi(false);
      }
    },
    [imageActions, handleRemoveBackground]
  );

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp'],
    },
    multiple: true,
    disabled: isLoadingApi,
    noClick: true,
  });

  /**
   * 处理下载事件。
   * @param downloadType 下载类型：'free' 为免费版本（压缩），'premium' 为付费版本（原质量）
   */
  const handleDownload = async (
    downloadType: 'free' | 'premium' = 'premium'
  ) => {
    if (!currentImageObject || !canvasEditorRef.current) {
      console.error('下载失败：没有当前图像或编辑器引用。');
      return;
    }

    try {
      const imageDataUrl =
        await canvasEditorRef.current.getDownloadableImageData();
      if (imageDataUrl) {
        // 从store获取当前图片信息
        const currentImage = useImageStore
          .getState()
          .images.get(currentImageId!);
        if (!currentImage) return;

        let finalImageDataUrl = imageDataUrl;
        let fileName = currentImage.name;

        // 如果是免费版本，进行压缩处理
        if (downloadType === 'free') {
          // 动态导入压缩和尺寸调整工具
          const { resizeImage } = await import('@/lib/imageUtils/imageResize');

          // 获取原始尺寸
          const img = new window.Image();
          await new Promise<void>((resolve, reject) => {
            img.onload = () => resolve();
            img.onerror = () => reject(new Error('Failed to load image'));
            img.src = imageDataUrl;
          });

          const originalWidth = img.naturalWidth;
          const originalHeight = img.naturalHeight;

          // 计算压缩后的尺寸（0.6倍）
          const compressedWidth = Math.round(originalWidth * 0.6);
          const compressedHeight = Math.round(originalHeight * 0.6);

          // 获取原始图片格式
          const originalFormat = currentImage.originalFormat || 'png';

          // 执行尺寸压缩，保持原有格式
          const resizeResult = await resizeImage(
            imageDataUrl,
            compressedWidth,
            compressedHeight,
            'fit',
            0.7, // 中等质量压缩
            originalFormat as 'png' | 'jpg' | 'webp' | 'bmp'
          );

          finalImageDataUrl = resizeResult.dataUrl;

          // 为免费版本添加文件名后缀
          const nameWithoutExt = fileName.includes('.')
            ? fileName.substring(0, fileName.lastIndexOf('.'))
            : fileName;
          const ext = fileName.includes('.')
            ? fileName.substring(fileName.lastIndexOf('.'))
            : `.${originalFormat}`;
          fileName = `${nameWithoutExt}_preview${ext}`;
        }
        // 付费版本保持原质量和原文件名，不需要额外处理

        const link = document.createElement('a');
        link.href = finalImageDataUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      } else {
        console.error('下载失败：未能从编辑器获取图像数据。');
      }
    } catch (error) {
      console.error('下载过程中发生错误:', error);
    }
  };

  // 删除后图片选择处理，包含自动去背逻辑
  const handleImageSelectAfterDelete = useCallback(
    async (imageId: string): Promise<void> => {
      const image = useImageStore.getState().images.get(imageId);
      if (!image) return;

      // 使用通用的自动背景去除函数
      await processImageBackgroundRemoval(image, handleRemoveBackground);
    },
    [processImageBackgroundRemoval, handleRemoveBackground]
  );

  // 监听背景图片清理事件
  useEffect(() => {
    const handleCleanupBackgroundImage = async (event: CustomEvent) => {
      const { url, id } = event.detail;

      try {
        // 跳过预设背景图片的清理
        if (id?.startsWith('preset-')) {
          console.log('跳过预设背景图片清理:', id);
          return;
        }

        console.log('收到背景图片清理事件:', { url, id });

        // 再次确认没有图片在使用这个背景图片
        const allImages = Array.from(useImageStore.getState().images.values());
        const stillInUse = allImages.some(
          img =>
            img.backgroundImageUrl === url ||
            (id && img.backgroundImageId === id)
        );

        if (stillInUse) {
          console.log('背景图片仍在使用，取消清理:', { url, id });
          return;
        }

        // 优先通过backgroundImageId查找，如果没有则通过URL查找
        const backgroundImage = id
          ? uploadedBackgroundImages.find(img => img.id === id)
          : uploadedBackgroundImages.find(img => img.url === url);

        if (backgroundImage) {
          console.log('删除背景图片:', backgroundImage.id);
          // 删除IndexedDB中的数据和更新状态
          await deleteBackgroundImage(backgroundImage.id);
        } else {
          console.log('未找到背景图片，可能已被删除:', { url, id });
          // 如果找不到，清理可能失效的blob URL
          if (url && url.startsWith('blob:')) {
            try {
              URL.revokeObjectURL(url);
              console.log('已清理失效的blob URL:', url);
            } catch (error) {
              console.error('清理失效blob URL失败:', error);
            }
          }
        }
      } catch (error) {
        console.error('清理自定义背景图片失败:', error);
      }
    };

    // 添加事件监听器
    window.addEventListener(
      'cleanupBackgroundImage',
      handleCleanupBackgroundImage as unknown as EventListener
    );

    return () => {
      // 清理事件监听器
      window.removeEventListener(
        'cleanupBackgroundImage',
        handleCleanupBackgroundImage as unknown as EventListener
      );
    };
  }, [uploadedBackgroundImages, deleteBackgroundImage]);

  /**
   * 检查并清理未使用的自定义背景图片
   */
  const checkAndCleanupUnusedBackgroundImage = useCallback(
    async (
      backgroundImage: { url: string; id?: string },
      deletedImageId?: string
    ) => {
      if (!backgroundImage.url.startsWith('blob:')) {
        return; // 只处理blob URL（自定义上传的图片）
      }

      // 预设背景图片不需要清理
      if (backgroundImage.id?.startsWith('preset-')) {
        return;
      }

      // 检查当前所有图片是否还有使用这个背景图片
      // 注意：需要排除正在删除的图片
      const allImages = Array.from(
        useImageStore.getState().images.values()
      ).filter(img => img.id !== deletedImageId);

      // 检查是否还有图片使用这个背景图片
      const stillInUse = allImages.some(
        img =>
          img.backgroundImageUrl === backgroundImage.url ||
          (backgroundImage.id && img.backgroundImageId === backgroundImage.id)
      );

      if (stillInUse) {
        return; // 如果仍在使用，直接返回，不执行任何清理操作
      }

      // 如果是自定义上传的背景图片，通知主组件清理
      try {
        window.dispatchEvent(
          new CustomEvent('cleanupBackgroundImage', {
            detail: {
              url: backgroundImage.url,
              id: backgroundImage.id,
            },
          })
        );
      } catch (error) {
        console.error('通知清理背景图片失败:', error);
      }
    },
    []
  );

  // 对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<string | null>(null);

  /**
   * 显示删除确认弹窗
   */
  const showDeleteConfirmation = useCallback((imageId: string) => {
    setImageToDelete(imageId);
    setDeleteDialogOpen(true);
  }, []);

  /**
   * 确认删除图片
   */
  const confirmDeleteImage = useCallback(async () => {
    if (!imageToDelete) return;
    try {
      await handleRemoveImage(imageToDelete);
    } catch (error) {
      console.error('删除图片失败:', error);
    } finally {
      setDeleteDialogOpen(false);
      setImageToDelete(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [imageToDelete]);

  /**
   * 取消删除
   */
  const cancelDeleteImage = useCallback(() => {
    setDeleteDialogOpen(false);
    setImageToDelete(null);
  }, []);

  /**
   * 从历史记录中移除一张图片。
   */
  const handleRemoveImage = useCallback(
    async (imageId: string) => {
      // 暂停历史记录跟踪，删除操作不应该被记录
      useImageStore.temporal.getState().pause();

      try {
        // 获取要删除的图片信息，用于资源清理
        const imageToDelete = useImageStore.getState().images.get(imageId);

        // 使用工具函数检查删除状态并获取清理操作
        const isCurrentImage = currentImageId === imageId;
        const isProcessing = processingImageIds?.has(imageId) || false;

        const { shouldWarnUser, warningMessage, cleanupActions } =
          cleanupImageDeletionState(
            imageId,
            imageToDelete
              ? {
                  previewUrl: imageToDelete.previewUrl,
                  processedUrl: imageToDelete.processedUrl || undefined,
                  backgroundImageUrl: imageToDelete.backgroundImageUrl,
                }
              : undefined,
            isCurrentImage,
            isProcessing
          );

        // 如果需要警告用户
        if (shouldWarnUser && warningMessage) {
          console.warn(`删除图片 ${imageId}: ${warningMessage}`);
        }

        // 检查是否使用了自定义背景图片，并检查是否需要清理
        let backgroundImageToCheck: { url: string; id?: string } | undefined;
        if (imageToDelete?.backgroundImageUrl?.startsWith('blob:')) {
          backgroundImageToCheck = {
            url: imageToDelete.backgroundImageUrl,
            id: imageToDelete.backgroundImageId,
          };
        }

        // 执行所有清理操作
        cleanupActions.forEach(action => {
          try {
            action();
          } catch (error) {
            console.error('清理操作失败:', error);
          }
        });

        // 获取删除前的图片列表
        const imagesBeforeDelete = Array.from(
          useImageStore.getState().images.keys()
        );

        // 如果删除的是当前选中的图片，需要处理选择切换
        if (currentImageId === imageId) {
          const remainingImages = imagesBeforeDelete.filter(
            id => id !== imageId
          );

          // 强制关闭橡皮擦弹窗和重置相关状态
          setForceCloseEraserTool(true);
          // setIsHandToolActive(false);

          if (remainingImages.length > 0) {
            // 选择下一张图片
            const nextImageId = remainingImages[0];

            // 如果提供了onImageSelect回调，使用它来处理图片选择
            // 这样可以触发自动去背逻辑（如果需要的话）
            if (handleImageSelectAfterDelete) {
              // 先更新选择状态，然后异步处理图片选择逻辑
              useImageStore.setState(state => {
                state.selectedImageIds.clear();
                state.selectedImageIds.add(nextImageId);
              });

              // 异步调用选择处理逻辑，可能包含自动去背
              setTimeout(() => {
                handleImageSelectAfterDelete(nextImageId).catch(error => {
                  console.error('处理删除后的图片选择失败:', error);
                });
              }, 100);
            } else {
              // 如果没有提供回调，直接切换到下一张图片（兼容模式）
              useImageStore.setState(state => {
                state.selectedImageIds.clear();
                state.selectedImageIds.add(nextImageId);
              });
            }
          } else {
            // 如果没有剩余图片，清空选择并重置所有UI状态
            useImageStore.setState(state => {
              state.selectedImageIds.clear();
            });

            // 重置所有相关的UI状态
            setCurrentImageObject(null);
            setCanvasWrapperStyle({});
            setWrapperSize({ width: 0, height: 0 });
            // setInitialScale(1);
            setCurrentScale(1);
            setIsCompareActive(false);
          }

          // 清除全局历史记录，确保撤销/重做状态正确
          clearHistory();

          // 重置橡皮擦弹窗状态
          setTimeout(() => setForceCloseEraserTool(false), 100);
        }

        // 执行删除操作（这会自动删除IndexedDB中的图片数据）
        imageActions.removeImage(imageId);

        // 检查是否需要清理未使用的自定义背景图片
        if (backgroundImageToCheck) {
          // 短暂延迟确保删除操作完成
          setTimeout(async () => {
            try {
              await checkAndCleanupUnusedBackgroundImage(
                backgroundImageToCheck,
                imageId // 传递被删除的图片ID
              );
            } catch (error) {
              console.error('清理未使用的背景图片失败:', error);
            }
          }, 100); // 减少延迟时间
        }
      } finally {
        // 恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
      }
    },

    [
      currentImageId,
      imageActions,
      processingImageIds,
      handleImageSelectAfterDelete,
      checkAndCleanupUnusedBackgroundImage,
    ]
  );

  // 处理图片选择，包括自动背景处理
  const handleImageSelect = async (imageId: string) => {
    const selectedImage = await handleSelectImage(imageId);

    // 使用通用的自动背景去除函数
    if (selectedImage) {
      await processImageBackgroundRemoval(
        selectedImage,
        handleRemoveBackground
      );
    }

    return selectedImage;
  };

  /**
   * 在历史记录中选择一张图片进行编辑。
   */
  const handleSelectImage = useCallback(async (imageId: string) => {
    // 切换图片时关闭橡皮擦弹窗
    setForceCloseEraserTool(true);
    // 使用一次 setState 调用来避免创建多条历史记录
    useImageStore.setState(state => {
      state.selectedImageIds.clear();
      state.selectedImageIds.add(imageId);
    });

    // 保存当前选中的图片ID到持久化存储
    const currentState = useImageStore.getState();
    if (currentState.isStorageInitialized) {
      try {
        await imageStorage.saveCurrentSelectedImageId(imageId);
        console.log('已保存选中图片ID:', imageId);
      } catch (error) {
        console.error('保存选中图片ID失败:', error);
      }
    }

    // 切换图片时清除全局历史记录，确保撤销/重做只针对当前图片
    clearHistory();

    // 重置强制关闭状态
    setTimeout(() => setForceCloseEraserTool(false), 100);

    // 检查所选图片是否为 original 状态，如果是则需要外部处理
    const selectedImage = useImageStore.getState().images.get(imageId);
    return selectedImage;
  }, []);
  console.log(images);

  return (
    <div className='h-full flex flex-col bg-gray-50 overflow-hidden relative select-none'>
      {/* 始终渲染的隐藏 input，用于文件选择 */}
      <input {...getInputProps()} />
      <div
        className={`h-full flex items-start ${!currentImageObject ? 'justify-center mt-8' : ''}`}
      >
        <div className='relative flex justify-center items-center flex-col'>
          {/* 未上传图片时的初始界面 */}
          {!currentImageObject ? (
            <div className='flex-1 flex flex-col items-center px-6 py-10'>
              <h1 className='text-2xl font-bold text-center text-gray-900 mb-8'>
                {singleImage('initial.uploadImageToRemove')}
              </h1>

              {/* 移动端上传区域 */}
              <div
                {...getRootProps()}
                className='w-full max-w-md bg-white rounded-2xl shadow-lg p-2 mb-8'
              >
                <div className='border-2 border-dashed border-gray-300 rounded-xl p-8 text-center'>
                  <Button
                    size='lg'
                    className='bg-yellow-400 text-gray-900 hover:bg-yellow-500 w-62 h-12 mt-6 mb-4'
                    onClick={e => {
                      e.stopPropagation();
                      open();
                    }}
                  >
                    <div className='size-5 bg-yellow-400 rounded-full flex items-center justify-center border-[1.5px] border-current'>
                      <Plus className='w-2 h-2 text-current' />
                    </div>
                    {singleImage('initial.uploadImage')}
                  </Button>

                  <p className='text-sm text-gray-500'>
                    {singleImage.rich('initial.pasteImageMobile', {
                      url: chunks => (
                        <Dialog
                          open={isUrlDialogOpen}
                          onOpenChange={setIsUrlDialogOpen}
                        >
                          <DialogTrigger asChild>
                            <span className='text-yellow-400 underline'>
                              {chunks}
                            </span>
                          </DialogTrigger>
                          <DialogContent className='sm:max-w-md'>
                            <DialogHeader>
                              <DialogTitle>
                                {singleImage('initial.pasteImageUrl')}
                              </DialogTitle>
                            </DialogHeader>
                            <div className='flex items-center space-x-2'>
                              <Input
                                placeholder={singleImage(
                                  'initial.pleaseInputImageUrl'
                                )}
                                value={inputUrl}
                                onChange={e => setInputUrl(e.target.value)}
                                onKeyDown={e => {
                                  if (e.key === 'Enter') {
                                    handleLoadFromUrl(inputUrl);
                                  }
                                }}
                                disabled={isLoadingUrl}
                                className='bg-[#F9FAFB] focus-visible::outline-none focus-visible:ring-0 focus:border-yellow-400'
                              />
                            </div>
                            <DialogFooter className='flex-row'>
                              <Button
                                variant='outline'
                                onClick={() => {
                                  setIsUrlDialogOpen(false);
                                  setInputUrl('');
                                }}
                                disabled={isLoadingUrl}
                                className='flex-1'
                              >
                                {common('cancel')}
                              </Button>
                              <Button
                                onClick={() => handleLoadFromUrl(inputUrl)}
                                disabled={!inputUrl.trim() || isLoadingUrl}
                                className='flex-1 text-gray-900'
                              >
                                {isLoadingUrl ? (
                                  <svg
                                    className='animate-spin -ms-1 me-2 h-4 w-4'
                                    xmlns='http://www.w3.org/2000/svg'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                  >
                                    <circle
                                      className='opacity-25'
                                      cx='12'
                                      cy='12'
                                      r='10'
                                      stroke='currentColor'
                                      strokeWidth='4'
                                    ></circle>
                                    <path
                                      className='opacity-75'
                                      fill='currentColor'
                                      d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                                    ></path>
                                  </svg>
                                ) : null}
                                {isLoadingUrl
                                  ? common('loading')
                                  : common('confirm')}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      ),
                    })}
                  </p>
                  <div className='text-xs text-gray-500 mt-13'>
                    <p>{singleImage('initial.supportedFormats')}</p>
                    <p className='flex mt-2 justify-center gap-2'>
                      {['JPG', 'JPEG', 'PNG', 'WebP'].map(
                        (imageType, index) => {
                          return (
                            <span
                              key={index}
                              className='w-12 h-[26px] bg-[#F9FAFB] border-1 border-[#E7E7E7] rounded text-gray-900 leading-[26px] text-center'
                            >
                              {imageType}
                            </span>
                          );
                        }
                      )}
                    </p>
                  </div>
                </div>
              </div>

              {/* 移动端示例图片 */}
              <div className='w-full max-w-md'>
                <h3 className='text-sm text-gray-600 text-center mb-4'>
                  {singleImage('initial.noImageTryThese')}
                </h3>
                <div className='flex justify-center gap-4'>
                  {SAMPLE_IMAGES.map(sample => (
                    <div
                      key={sample.id}
                      className='cursor-pointer'
                      onClick={() =>
                        handleLoadSampleImage(
                          sample.url,
                          sample.name,
                          sample.processedUrl
                        )
                      }
                    >
                      <div className='w-16 h-16 rounded-lg overflow-hidden bg-gray-300 shadow-md'>
                        <Image
                          src={sample.url}
                          alt={sample.name}
                          className='w-full h-full object-cover'
                          width={64}
                          height={64}
                        />
                      </div>
                    </div>
                  ))}
                </div>
                <p className='text-sm text-gray-600 text-center mt-4'>
                  {singleImage('initial.recaptchaNotice')}
                </p>
              </div>
            </div>
          ) : (
            <div>
              {/* 历史记录栏：仅当有图片时显示 */}
              {images.length > 0 && (
                <ImageHistoryBar
                  images={images}
                  selectedImageIds={
                    new Set([currentImageId].filter(Boolean) as string[])
                  }
                  isLoadingApi={isLoadingApi}
                  currentImageId={currentImageId}
                  open={open}
                  handleSelectImage={handleImageSelect}
                  showDeleteConfirmation={showDeleteConfirmation}
                  deleteDialogOpen={deleteDialogOpen}
                  setDeleteDialogOpen={setDeleteDialogOpen}
                  confirmDeleteImage={confirmDeleteImage}
                  cancelDeleteImage={cancelDeleteImage}
                  imagesCount={images.length}
                />
              )}
              {/* Canvas 编辑器容器 */}
              <div className='relative px-4' style={canvasWrapperStyle}>
                <MobileCanvasImageEditor
                  ref={canvasEditorRef}
                  originalImgObject={currentImageObject}
                  processedUrl={processedUrl}
                  backgroundColor={selectedBackgroundColor}
                  backgroundImageUrl={backgroundImageUrl}
                  isCompareModeActive={isCompareActive}
                  scale={currentScale}
                  onScaleChangeRequest={setCurrentScale}
                  isBlurEnabled={isBlurEnabled}
                  blurAmount={blurAmount}
                  containerWidth={wrapperSize.width}
                  containerHeight={wrapperSize.height}
                  isEraseMode={isEraseMode}
                  isRestoreMode={isRestoreMode}
                  eraseBrushSize={eraseBrushSize}
                  onEraseOperationComplete={handleEraseOperationComplete}
                />
                {/* API 加载遮罩 */}
                {isLoadingApi && currentImageObject && (
                  <div className='absolute start-4 end-4 top-0 bottom-0 bg-black/50 bg-opacity-30 flex items-center justify-center z-20 rounded-lg pointer-events-auto'>
                    <StarryNightLoading starColor='yellow' starCount={50} />
                  </div>
                )}
              </div>
              {/* 底部工具栏 */}
              <div className='w-full flex justify-end items-center mt-2 px-4'>
                <div className='bg-white border border-[#e7e7e7] rounded-full h-10 px-4 flex items-center space-x-4'>
                  {isLoadingApi ? (
                    // 加载中的骨架屏
                    <>
                      <div className='w-10 h-10 bg-gray-200 rounded animate-pulse'></div>
                      <div className='w-10 h-10 bg-gray-200 rounded animate-pulse'></div>
                      <div className='w-10 h-10 bg-gray-200 rounded animate-pulse'></div>
                    </>
                  ) : (
                    // 工具栏按钮
                    <>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant='ghost'
                            onClick={handleUndo}
                            disabled={
                              !temporalState ||
                              temporalState.pastStates.length === 0
                            }
                            className='w-10 h-10 m-0 px-0'
                          >
                            <Image
                              src='/apps/icons/undo.svg'
                              alt='undo'
                              width={24}
                              height={24}
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {common('previousStep')}
                        </TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant='ghost'
                            onClick={handleRedo}
                            disabled={
                              !temporalState ||
                              temporalState.futureStates.length === 0
                            }
                            className='w-10 h-10 m-0 px-0'
                          >
                            <Image
                              src='/apps/icons/redo.svg'
                              alt='redo'
                              width={24}
                              height={24}
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{common('nextStep')}</TooltipContent>
                      </Tooltip>
                      <span className='w-0 text-center text-[#E7E7E7] mx-2'>
                        |
                      </span>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant='ghost'
                            // variant={isCompareActive ? 'secondary' : 'outline'}
                            size='icon'
                            onMouseDown={() => setIsCompareActive(true)}
                            onMouseUp={() => setIsCompareActive(false)}
                            onTouchStart={() => setIsCompareActive(true)}
                            onTouchEnd={() => setIsCompareActive(false)}
                            className='w-10 h-10 m-0'
                          >
                            <Image
                              src='/apps/icons/compare.svg'
                              alt='compare'
                              width={24}
                              height={24}
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {common('compareOriginal')}
                        </TooltipContent>
                      </Tooltip>
                    </>
                  )}
                </div>
              </div>
              {/* 下方编辑面板 */}
              <div
                className={`px-4 flex flex-col flex-shrink-0 space-y-1 transition-opacity duration-300 ${isLoadingApi ? 'opacity-50 pointer-events-none' : 'opacity-100'}
            `}
              >
                {/* Download 按钮 */}
                <div className='mt-6'>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        className='bg-[#ffcc03] hover:bg-[#FFE343] text-[#121212] w-full h-12 rounded-lg text-[16px] font-medium'
                        disabled={
                          !currentImageObject ||
                          isLoadingApi ||
                          !currentImageFromHistory?.processedUrl
                        }
                      >
                        <div className='flex items-center space-x-2'>
                          <span>{common('download')}</span>
                          <Image
                            src='/apps/icons/downOutlined.svg'
                            alt='downloadArrow'
                            width={24}
                            height={24}
                          />
                        </div>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className='w-[340px] p-0 border-0 rounded-xl'
                      align='start'
                    >
                      <div className='bg-white border border-[#e7e7e7] rounded-xl shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)]'>
                        <div className='flex flex-col p-2 space-y-1'>
                          {/* Preview Option - Free */}
                          <div
                            className='bg-[rgba(255,204,3,0.3)] rounded-lg p-4'
                            onClick={() => handleDownload('free')}
                          >
                            <div className='flex items-center justify-between'>
                              <div className='flex items-center space-x-3'>
                                <div className='relative shrink-0 size-6'>
                                  <Image
                                    src='/apps/icons/preview.svg'
                                    alt='preview'
                                    width={24}
                                    height={24}
                                  />
                                </div>
                                <div className='flex flex-col  text-[#121212]'>
                                  <span className='text-[16px] font-medium'>
                                    {singleImage('interface.previewSize')}
                                  </span>
                                  <span className='text-[14px] text-[#878787]'>
                                    {formatImageDimensions(
                                      currentImageFromHistory,
                                      'free',
                                      wrapperSize
                                    )}
                                  </span>
                                </div>
                              </div>
                              <div className='bg-[#e7e7e7] w-[69px] rounded-full px-3 text-center'>
                                <span className='text-[14px] font-medium text-[#121212] leading-7'>
                                  {common('free')}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Max Quality Option - Premium */}
                          <div
                            className='rounded-lg p-4 hover:bg-gray-50 cursor-pointer'
                            onClick={() => handleDownload('premium')}
                          >
                            <div className='flex items-center justify-between'>
                              <div className='flex items-center space-x-3'>
                                <div className='relative shrink-0 size-6'>
                                  <Image
                                    src='/apps/icons/max.svg'
                                    alt='max'
                                    width={24}
                                    height={24}
                                  />
                                </div>
                                <div className='flex flex-col  text-[#121212]'>
                                  <span className='text-[16px] font-medium'>
                                    {singleImage('interface.maxQualitySize')}
                                  </span>
                                  <span className='text-[14px] text-[#878787]'>
                                    {formatImageDimensions(
                                      currentImageFromHistory,
                                      'premium',
                                      wrapperSize
                                    )}
                                  </span>
                                </div>
                              </div>
                              <div className='bg-[#ffcc03] w-[69px] rounded-full px-3 text-center'>
                                <span className='text-[14px] font-medium text-[#000000] leading-7'>
                                  {common('unlock')}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* 功能选项列表 */}
                <div className='flex mt-14 justify-between'>
                  {/* Change Background Colors */}
                  <MobileBackgroundColorPicker
                    currentColor={selectedBackgroundColor}
                    onChangeColor={color => {
                      if (selectedBackgroundColor !== color) {
                        // 选择背景颜色时，清除背景图片（互斥逻辑）
                        updateCurrentImageSettings({
                          backgroundColor: color,
                          backgroundImageUrl: undefined,
                        });
                      }
                    }}
                  >
                    <div className='rounded-lg p-2 hover:bg-[#F0F0F0] cursor-pointer'>
                      <div className='flex flex-col items-center'>
                        <div className='relative shrink-0 size-12'>
                          <div className='absolute border bg-white border-solid border-[#E7E7E7] inset-0 pointer-events-none rounded-full' />
                          <div className='flex items-center justify-center relative size-full'>
                            <div className='size-7'>
                              <Image
                                src='/apps/icons/changeColors.svg'
                                alt='changeColors'
                                width={28}
                                height={28}
                              />
                            </div>
                          </div>
                        </div>
                        <div className='flex flex-col items-center leading-[1.5] not-italic text-[#121212] text-sm text-center'>
                          {mobile('bgColors')}
                        </div>
                      </div>
                    </div>
                  </MobileBackgroundColorPicker>
                  {/* Change Background Photos */}
                  <MobileBackgroundImagePicker
                    currentBackgroundImageUrl={backgroundImageUrl}
                    onSelectImage={url => {
                      if (backgroundImageUrl !== url) {
                        // 选择背景图片时，清除背景颜色（互斥逻辑）
                        updateCurrentImageSettings({
                          backgroundImageUrl: url,
                          backgroundColor: url
                            ? 'transparent'
                            : selectedBackgroundColor,
                        });
                      }
                    }}
                    onFileUpload={async file => {
                      try {
                        const newImage = await addBackgroundImage(file);
                        // 上传新背景图片时，清除背景颜色（互斥逻辑）
                        updateCurrentImageSettings({
                          backgroundImageUrl: newImage.url,
                          backgroundImageId: newImage.id,
                          backgroundColor: 'transparent',
                        });
                      } catch (error) {
                        console.error('上传背景图片失败:', error);
                      }
                    }}
                    uploadedImages={uploadedBackgroundImages}
                  >
                    <div className='rounded-lg p-2 hover:bg-[#F0F0F0] cursor-pointer'>
                      <div className='flex flex-col items-center'>
                        <div className='relative shrink-0 size-12'>
                          <div className='absolute border bg-white border-solid border-[#E7E7E7] inset-0 pointer-events-none rounded-full' />
                          <div className='flex items-center justify-center relative size-full'>
                            <div className='size-7'>
                              <Image
                                src='/apps/icons/changePhotos.svg'
                                alt='changePhotos'
                                width={28}
                                height={28}
                              />
                            </div>
                          </div>
                        </div>
                        <div className='flex justify-center leading-[1.5] not-italic text-[#121212] text-sm text-center'>
                          {mobile('bgPhotos')}
                        </div>
                      </div>
                    </div>
                  </MobileBackgroundImagePicker>
                  {/* Erase / Restore */}
                  <MbileEraserTool
                    isEraseMode={isEraseMode}
                    isRestoreMode={isRestoreMode}
                    eraseBrushSize={eraseBrushSize}
                    forceClose={forceCloseEraserTool}
                    onEraseModeChange={(isEraseMode, isRestoreMode) => {
                      updateCurrentImageSettings({
                        isEraseMode,
                        isRestoreMode,
                      });
                    }}
                    onBrushSizeChange={size => {
                      updateCurrentImageSettings({
                        eraseBrushSize: size,
                      });
                    }}
                  >
                    <div className='rounded-lg p-2 hover:bg-[#F0F0F0] cursor-pointer'>
                      <div className='flex flex-col items-center'>
                        <div className='relative shrink-0 size-12'>
                          <div className='absolute border bg-white border-solid border-[#E7E7E7] inset-0 pointer-events-none rounded-full' />
                          <div className='flex items-center justify-center relative size-full'>
                            <div className='size-7'>
                              <Image
                                src='/apps/icons/erase.svg'
                                alt='erase'
                                width={28}
                                height={28}
                              />
                            </div>
                          </div>
                        </div>
                        <div className='flex justify-center leading-[1.5] not-italic text-[#121212] text-sm text-center'>
                          {singleImage('interface.eraseRestore')}
                        </div>
                      </div>
                    </div>
                  </MbileEraserTool>
                  {/* Blur Background */}
                  <MobileBackgroundBlurPicker
                    isBlurEnabled={isBlurEnabled}
                    blurAmount={blurAmount}
                    onBlurSettingsChange={settings => {
                      updateCurrentImageSettings({
                        isBlurEnabled: settings.isBlurEnabled,
                        blurAmount: settings.blurAmount,
                      });
                    }}
                  >
                    <div className='rounded-lg p-2 hover:bg-[#F0F0F0] cursor-pointer'>
                      <div className='flex flex-col items-center'>
                        <div className='relative shrink-0 size-12'>
                          <div className='absolute border bg-white border-solid border-[#E7E7E7] inset-0 pointer-events-none rounded-full' />
                          <div className='flex items-center justify-center relative size-full'>
                            <div className='size-6'>
                              <Image
                                src='/apps/icons/blur.svg'
                                alt='blur'
                                width={24}
                                height={24}
                              />
                            </div>
                          </div>
                        </div>
                        <div className='flex justify-center leading-[1.5] not-italic text-[#121212] text-sm text-center'>
                          {mobile('blurBG')}
                        </div>
                      </div>
                    </div>
                  </MobileBackgroundBlurPicker>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

import { useCallback, useState, useEffect } from 'react';
import { useDropzone, type FileRejection } from 'react-dropzone';
import { removeBackground } from '@/api';
import { useImageStore } from '@/store/imageStore';
import { useAuthStore } from '@/store/accountStore';
import { useTips } from '@/components/ui/Tips';
import {
  validateImageFormat,
  createImageFromSource,
} from '@/lib/imageUtils/imageState';
import {
  MAX_BATCH_IMAGES_LIMIT,
  FREE_USER_BATCH_LIMIT,
} from '@/config/constants';
import { useTranslations } from 'next-intl';

/**
 * 批量编辑专用的图片上传hook
 * 与普通上传的区别：上传后不自动去除背景，保持original状态
 * 非会员用户超过限制的图片会被设置为locked状态
 * 支持IndexedDB自动保存功能
 */
export const useBatchImageUpload = (isVipUser: boolean = false) => {
  const [isLoadingApi, setIsLoadingApi] = useState(false);
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);
  const [processingImageIds, setProcessingImageIds] = useState<Set<string>>(
    new Set()
  );
  const messages = useTranslations('messages');
  const { showTips } = useTips();

  /**
   * 添加图片到处理状态
   */
  const addProcessingImages = useCallback((imageIds: string[]) => {
    setProcessingImageIds(prev => {
      const newSet = new Set(prev);
      imageIds.forEach(id => newSet.add(id));
      return newSet;
    });
  }, []);

  /**
   * 从处理状态中移除图片
   */
  const removeProcessingImages = useCallback((imageIds: string[]) => {
    setProcessingImageIds(prev => {
      const newSet = new Set(prev);
      imageIds.forEach(id => newSet.delete(id));
      return newSet;
    });
  }, []);

  /**
   * 清空所有处理状态
   */
  const clearProcessingImages = useCallback(() => {
    setProcessingImageIds(new Set());
  }, []);

  /**
   * 更新图片锁定状态 - 保持最新的3张图片为可处理状态
   * 非会员用户：最新的3张图片保持original状态，其余设为locked
   * 会员用户：所有图片保持original状态
   */
  const updateImageLockStatus = useCallback(() => {
    if (isVipUser) return; // 会员用户不需要锁定

    const imageStore = useImageStore.getState();
    const allImages = Array.from(imageStore.images.values());

    // 按上传时间排序，最新的在前
    const sortedImages = allImages.sort((a, b) => b.timestamp - a.timestamp);

    // 更新每张图片的状态
    sortedImages.forEach((image, index) => {
      const shouldBeUnlocked = index < FREE_USER_BATCH_LIMIT;
      const currentStatus = image.status;

      if (shouldBeUnlocked && currentStatus === 'locked') {
        // 解锁：从locked变为original
        imageStore.updateImage(image.id, { status: 'original' });
        console.log(`图片 ${image.name} 解锁为 original 状态`);
      } else if (!shouldBeUnlocked && currentStatus === 'original') {
        // 锁定：从original变为locked
        imageStore.updateImage(image.id, { status: 'locked' });
        console.log(`图片 ${image.name} 锁定为 locked 状态`);
      }
    });
  }, [isVipUser]);

  /**
   * 批量去除背景 - 仅处理选中的图片
   */
  const batchRemoveBackground = useCallback(
    async (imageIds: string[]) => {
      if (imageIds.length === 0) return;

      setIsLoadingApi(true);

      // 添加所有要处理的图片ID到处理列表
      setProcessingImageIds(prev => {
        const newSet = new Set(prev);
        imageIds.forEach(id => newSet.add(id));
        return newSet;
      });

      try {
        // 并发处理所有图片，但不立即更新状态

        const promises = imageIds.map(async imageId => {
          const image = useImageStore.getState().images.get(imageId);
          if (!image || image.processedUrl || image.status === 'locked') {
            return { imageId, success: false, error: 'Skipped' };
          }

          try {
            // 将文件转换为 Data URL
            let imageDataUrl: string;
            if (image.file) {
              imageDataUrl = await new Promise<string>((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result as string);
                reader.onerror = reject;
                reader.readAsDataURL(image.file!);
              });
            } else {
              imageDataUrl = image.previewUrl;
            }

            // 调用背景去除API
            const imageBlob = await removeBackground({ image: imageDataUrl });
            const objectURL = URL.createObjectURL(imageBlob);

            // 标记用户已执行去背操作
            useAuthStore.getState().markBackgroundRemovalPerformed();

            console.log(`批量背景去除完成: ${imageId}`);
            return { imageId, success: true, processedUrl: objectURL };
          } catch (error) {
            console.error(`批量处理图片背景去除失败: ${imageId}`, error);
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            showTips('error', `Failed to process image: ${image.name}`, 3000);
            return { imageId, success: false, error: errorMessage };
          } finally {
            // 从处理列表中移除
            setProcessingImageIds(prev => {
              const newSet = new Set(prev);
              newSet.delete(imageId);
              return newSet;
            });
          }
        });

        const processResults = await Promise.all(promises);

        // 准备批量更新数据
        const batchUpdates = processResults
          .filter(result => result.success && result.processedUrl)
          .map(result => ({
            imageId: result.imageId,
            updates: {
              processedUrl: result.processedUrl!,
              status: 'bg-removed' as const,
            },
          }));

        const failedUpdates = processResults
          .filter(result => !result.success && result.error !== 'Skipped')
          .map(result => ({
            imageId: result.imageId,
            updates: {
              status: 'bg-remove-failed' as const,
            },
          }));

        // 使用批量更新方法，这样只会创建一个历史记录项
        if (batchUpdates.length > 0) {
          useImageStore.getState().performBatchUpdate(batchUpdates);
          console.log(
            `🔄 [batchRemoveBackground] 批量更新成功处理的 ${batchUpdates.length} 张图片`
          );
        }

        if (failedUpdates.length > 0) {
          useImageStore.getState().performBatchUpdate(failedUpdates);
          console.log(
            `🔄 [batchRemoveBackground] 批量更新失败的 ${failedUpdates.length} 张图片`
          );
        }

        // 根据处理结果显示不同的提示消息
        const totalImages = imageIds.length;
        const successCount = batchUpdates.length;
        const failedCount = failedUpdates.length;

        if (failedCount === 0) {
          // 全部成功
          showTips('success', messages('batchEditor.allProcessedSuccessfully'));
        } else if (successCount > 0) {
          // 部分失败
          showTips(
            'warning',
            messages('batchEditor.someImagesFailed', {
              failed: failedCount,
              total: totalImages,
            })
          );
        } else {
          // 全部失败
          showTips('error', messages('batchEditor.batchFailed'));
        }
      } catch (error) {
        console.error('批量处理失败:', error);
        showTips('error', messages('batchEditor.batchFailed'));
      } finally {
        setIsLoadingApi(false);
      }
    },
    [showTips]
  );

  /**
   * 处理从剪贴板粘贴图片 - 不自动去除背景
   */
  const handlePasteFromClipboard = useCallback(
    async (file: File) => {
      // 检查图片数量限制
      const currentImages = useImageStore.getState().images;
      const currentCount = currentImages.size;
      if (currentCount >= MAX_BATCH_IMAGES_LIMIT) {
        showTips(
          'error',
          messages('singleImage.imagesExceedLimit', {
            count: MAX_BATCH_IMAGES_LIMIT,
          }),
          4000
        );
        return;
      }

      // 验证文件格式和大小
      const validation = validateImageFormat(file);
      if (!validation.isValid) {
        if (validation.error === 'file-too-large') {
          showTips('error', messages('batchEditor.fileSizeMaximum'), 3000);
        } else {
          showTips(
            'error',
            messages('singleImage.unsupportedImageFormat'),
            3000
          );
        }
        return;
      }

      setIsLoadingApi(true);

      try {
        // 暂停历史记录跟踪，因为这是初始化过程
        useImageStore.temporal.getState().pause();

        const newImage = await createImageFromSource(file);
        useImageStore.getState().addImage(newImage);

        // 选中新添加的图片
        useImageStore.getState().clearSelection();
        useImageStore.getState().toggleImageSelection(newImage.id);

        // 更新图片锁定状态（保持最新3张可用）
        updateImageLockStatus();

        console.log(`从剪贴板粘贴的图片已上传: ${newImage.name}`);
      } catch (error) {
        console.error('Error processing pasted file:', error);
        showTips('error', 'Failed to process pasted image', 3000);
      } finally {
        // 恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingApi(false);
      }
    },
    [showTips, updateImageLockStatus]
  );

  /**
   * 从URL加载图片 - 不自动去除背景
   */
  const handleLoadFromUrl = useCallback(
    async (url: string) => {
      if (!url.trim()) return;

      setIsLoadingUrl(true);
      setIsLoadingApi(true);

      // 暂停历史记录跟踪，因为这是初始化过程
      useImageStore.temporal.getState().pause();

      try {
        let blob: Blob;

        // 尝试多种方法获取图片
        try {
          // 方法1：直接fetch（适用于同源或支持CORS的图片）
          const response = await fetch(url, {
            mode: 'cors',
            headers: {
              Accept: 'image/*',
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          blob = await response.blob();
        } catch (corsError) {
          console.warn('直接fetch失败，使用服务端代理:', corsError);

          // 方法2：使用我们自己的服务端代理API
          try {
            const proxyResponse = await fetch(
              `/apps/api/proxy-image?url=${encodeURIComponent(url)}`
            );

            if (!proxyResponse.ok) {
              const errorData = await proxyResponse
                .json()
                .catch(() => ({ error: '代理服务器错误' }));
              throw new Error(errorData.error || '代理请求失败');
            }

            blob = await proxyResponse.blob();
          } catch (proxyError) {
            console.warn('服务端代理失败，尝试Canvas方式:', proxyError);

            // 方法3：使用Canvas和图片元素（最后的尝试）
            try {
              const img = new Image();
              img.crossOrigin = 'anonymous';

              await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = () => {
                  // 尝试不设置crossOrigin
                  const img2 = new Image();
                  img2.onload = resolve;
                  img2.onerror = reject;
                  img2.src = url;
                };
                img.src = url;
              });

              // 创建Canvas并转换为blob
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              if (!ctx) {
                throw new Error('无法创建Canvas上下文');
              }

              canvas.width = img.naturalWidth;
              canvas.height = img.naturalHeight;
              ctx.drawImage(img, 0, 0);

              blob = await new Promise<Blob>((resolve, reject) => {
                canvas.toBlob(result => {
                  if (result) {
                    resolve(result);
                  } else {
                    reject(new Error('Canvas转换失败'));
                  }
                }, 'image/png');
              });
            } catch (canvasError) {
              throw new Error(
                `无法加载图片：CORS限制或图片不存在。详情: ${canvasError}`
              );
            }
          }
        }

        // 验证blob类型
        if (!blob.type.startsWith('image/')) {
          // 尝试从URL推断图片类型
          const ext = url.split('.').pop()?.toLowerCase();
          const mimeTypes: { [key: string]: string } = {
            jpg: 'image/jpeg',
            jpeg: 'image/jpeg',
            png: 'image/png',
            webp: 'image/webp',
            gif: 'image/gif',
          };

          if (ext && mimeTypes[ext]) {
            blob = new Blob([blob], { type: mimeTypes[ext] });
          } else {
            throw new Error('无法识别的图片格式');
          }
        }

        // 从URL创建文件名
        const urlPath = new URL(url).pathname;
        const fileName = urlPath.split('/').pop() || `url_image_${Date.now()}`;
        const file = new File([blob], fileName, { type: blob.type });

        const newImage = await createImageFromSource(file);
        useImageStore.getState().addImage(newImage);

        // 默认选中新添加的图片
        useImageStore.getState().clearSelection();
        useImageStore.getState().toggleImageSelection(newImage.id);

        // 更新图片锁定状态（保持最新3张可用）
        updateImageLockStatus();

        console.log(`从URL加载的图片 ${fileName} 已上传，等待状态更新`);
      } catch (error) {
        console.error('从URL加载图片失败:', error);
        showTips('error', messages('singleImage.unableToOpenImage'));
      } finally {
        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingUrl(false);
        setIsLoadingApi(false);
      }
    },
    [showTips, updateImageLockStatus, messages]
  );

  /**
   * 粘贴事件监听器
   */
  useEffect(() => {
    const handlePaste = async (e: ClipboardEvent) => {
      // 只在没有焦点在输入框时处理粘贴
      const activeElement = document.activeElement;
      if (
        activeElement &&
        (activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.hasAttribute('contenteditable'))
      ) {
        return;
      }

      const items = e.clipboardData?.items;
      if (!items) return;

      // 首先尝试获取图片文件
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf('image') !== -1) {
          e.preventDefault();
          const file = item.getAsFile();
          if (file) {
            handlePasteFromClipboard(file);
          }
          return; // 找到图片就直接返回，不再处理URL
        }
      }

      // 如果没有图片，尝试获取文本（可能是URL）
      try {
        const text = await navigator.clipboard.readText();
        if (
          text &&
          (text.startsWith('http://') || text.startsWith('https://'))
        ) {
          // 简单检查是否可能是图片URL
          const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
          const lowerText = text.toLowerCase();
          const likelyImageUrl =
            imageExtensions.some(ext => lowerText.includes(ext)) ||
            lowerText.includes('image') ||
            lowerText.includes('photo') ||
            lowerText.includes('pic') ||
            text.match(/\.(jpg|jpeg|png|webp|gif)(\?|$)/i);

          if (likelyImageUrl) {
            e.preventDefault();
            // 直接调用URL加载功能
            await handleLoadFromUrl(text);
          }
        }
      } catch (clipboardError) {
        // 静默处理剪贴板读取错误
        console.debug('读取剪贴板文本失败:', clipboardError);
      }
    };

    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, [handlePasteFromClipboard, handleLoadFromUrl]);

  /**
   * 处理文件拖拽上传 - 不自动去除背景
   */
  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: FileRejection[]) => {
      // 处理被拒绝的文件
      rejectedFiles.forEach(({ errors }) => {
        errors.forEach(error => {
          if (error.code === 'file-too-large') {
            showTips('error', messages('batchEditor.fileSizeMaximum'), 3000);
          } else {
            showTips(
              'error',
              messages('singleImage.unsupportedImageFormat'),
              3000
            );
          }
        });
      });

      if (acceptedFiles.length === 0) return;

      // 检查图片数量限制
      const currentImages = useImageStore.getState().images;
      const currentCount = currentImages.size;
      const totalCount = currentCount + acceptedFiles.length;
      if (totalCount > MAX_BATCH_IMAGES_LIMIT) {
        showTips(
          'error',
          messages('singleImage.imagesExceedLimit', {
            count: MAX_BATCH_IMAGES_LIMIT,
          }),
          4000
        );
        return;
      }

      const validFiles = acceptedFiles.filter(file => {
        const validation = validateImageFormat(file);
        if (!validation.isValid) {
          showTips(
            'error',
            `${file.name}: ${validation.error || 'Invalid file'}`,
            3000
          );
          return false;
        }
        return true;
      });

      if (validFiles.length === 0) return;

      setIsLoadingApi(true);

      const processAllFiles = async () => {
        // 暂停历史记录跟踪，因为这是初始化过程
        useImageStore.temporal.getState().pause();

        for (let i = 0; i < validFiles.length; i++) {
          const file = validFiles[i];
          try {
            const newImage = await createImageFromSource(file);
            useImageStore.getState().addImage(newImage);

            // 第一张图片立即选中
            if (i === 0) {
              useImageStore.getState().clearSelection();
              useImageStore.getState().toggleImageSelection(newImage.id);
            }

            console.log(`图片 ${file.name} 已上传，等待状态更新`);
          } catch (error) {
            console.error('Error processing file:', error);
            showTips('error', messages('singleImage.imageUploadFailed'), 3000);
          }
        }

        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();

        // 更新图片锁定状态（保持最新3张可用）
        updateImageLockStatus();

        setIsLoadingApi(false);
      };

      processAllFiles();
    },
    [showTips, updateImageLockStatus, messages]
  );

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/webp': ['.webp'],
    },
    multiple: true,
    disabled: isLoadingApi,
    noClick: true,
    validator: file => {
      const validation = validateImageFormat(file);
      if (!validation.isValid) {
        if (validation.error === 'file-too-large') {
          return {
            code: 'file-too-large',
            message: '1',
          };
        } else {
          return {
            code: 'invalid-file-type',
            message: 'Only JPG, JPEG, PNG, and WebP formats are supported',
          };
        }
      }
      return null;
    },
  });

  return {
    // 上传相关
    isDragActive,
    getInputProps,
    getRootProps,
    open,
    handleLoadFromUrl,

    // 批量处理相关
    batchRemoveBackground,

    // 处理状态管理
    addProcessingImages,
    removeProcessingImages,
    clearProcessingImages,

    // 状态
    isLoadingApi,
    isLoadingUrl,
    processingImageIds,
  };
};

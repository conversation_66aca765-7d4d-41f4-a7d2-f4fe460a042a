import { useCallback, useState, useEffect } from 'react';
import { useDropzone, type FileRejection } from 'react-dropzone';
import { removeBackground } from '@/api';
import { useImageStore } from '@/store/imageStore';
import { useAuthStore } from '@/store/accountStore';
import { useTips } from '@/components/ui/Tips';
import {
  validateImageFormat,
  createImageFromSource,
} from '@/lib/imageUtils/imageState';
import { MAX_SINGLE_IMAGES_LIMIT } from '@/config/constants';
import { useTranslations } from 'next-intl';
/**
 * 通用的自动背景去除函数
 */
export const processImageBackgroundRemoval = async (
  image: {
    id: string;
    file?: File;
    previewUrl: string;
    status: string;
    processedUrl?: string | null;
  },
  handleRemoveBackground: (
    imageDataUrl: string,
    imageId?: string
  ) => Promise<string | null>
): Promise<boolean> => {
  if (image.status !== 'original' || image.processedUrl) {
    return false; // 不需要处理
  }

  try {
    console.log(`检测到 original 状态图片，开始自动去除背景: ${image.id}`);

    // 将文件转换为 Data URL
    let imageDataUrl: string;
    if (image.file) {
      imageDataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(image.file!);
      });
    } else {
      imageDataUrl = image.previewUrl;
    }

    // 调用背景去除API
    const resultUrl = await handleRemoveBackground(imageDataUrl, image.id);

    if (resultUrl) {
      // 更新 store
      useImageStore.getState().updateImage(image.id, {
        processedUrl: resultUrl,
        status: 'bg-removed',
      });

      console.log(`自动背景去除完成: ${image.id}`);
      return true;
    } else {
      // API调用失败时，错误状态已经在handleRemoveBackground中设置
      console.log(`自动背景去除失败: ${image.id}`);
      return false;
    }
  } catch (error) {
    console.error(`自动处理图片背景去除失败: ${image.id}`, error);
    // 确保设置错误状态
    useImageStore.getState().updateImage(image.id, {
      status: 'bg-remove-failed',
    });
  }

  return false;
};

export const useImageUpload = () => {
  const { showTips } = useTips();
  const imageActions = useImageStore.getState();
  const images = Array.from(useImageStore(s => s.images).values());
  const messages = useTranslations('messages');
  const [isLoadingApi, setIsLoadingApi] = useState(false);
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);
  // 跟踪正在处理的图片ID，用于处理删除时的状态清理
  const [processingImageIds, setProcessingImageIds] = useState<Set<string>>(
    new Set()
  );

  /**
   * 调用 API 移除图片背景
   */
  const handleRemoveBackground = useCallback(
    async (imageDataUrl: string, imageId?: string) => {
      setIsLoadingApi(true);

      // 如果提供了imageId，将其添加到正在处理的列表中
      if (imageId) {
        setProcessingImageIds(prev => new Set(prev).add(imageId));
      }

      try {
        const imageBlob = await removeBackground({ image: imageDataUrl });
        const objectURL = URL.createObjectURL(imageBlob);

        // 标记用户已执行去背操作
        useAuthStore.getState().markBackgroundRemovalPerformed();

        return objectURL;
      } catch (error) {
        console.error('移除背景失败:', error);

        // 如果提供了imageId，更新图片状态为失败
        if (imageId) {
          useImageStore.getState().updateImage(imageId, {
            status: 'bg-remove-failed',
          });
        }

        return null;
      } finally {
        // 清理状态
        setIsLoadingApi(false);
        if (imageId) {
          setProcessingImageIds(prev => {
            const newSet = new Set(prev);
            newSet.delete(imageId);
            return newSet;
          });
        }
      }
    },
    []
  );

  /**
   * 处理从剪贴板粘贴图片
   */
  const handlePasteFromClipboard = useCallback(
    async (file: File) => {
      // 检查图片数量限制
      const currentImageCount = images.length;
      if (currentImageCount >= MAX_SINGLE_IMAGES_LIMIT) {
        showTips(
          'error',
          messages('singleImage.imagesExceedLimit', {
            count: MAX_SINGLE_IMAGES_LIMIT,
          }),
          4000
        );
        return;
      }

      // 验证文件格式和大小
      const validation = validateImageFormat(file);
      if (!validation.isValid) {
        if (validation.error === 'file-too-large') {
          showTips('error', messages('batchEditor.fileSizeMaximum'), 3000);
        } else {
          showTips(
            'error',
            messages('singleImage.unsupportedImageFormat'),
            3000
          );
        }
        return;
      }

      setIsLoadingApi(true);

      try {
        // 暂停历史记录跟踪，因为这是初始化过程
        useImageStore.temporal.getState().pause();

        const newImage = await createImageFromSource(file);
        imageActions.addImage(newImage);

        // 选中新添加的图片
        imageActions.clearSelection();
        imageActions.toggleImageSelection(newImage.id);

        // 自动处理背景去除
        await processImageBackgroundRemoval(newImage, handleRemoveBackground);
      } catch (error) {
        console.error('Error processing pasted file:', error);
        showTips('error', 'Failed to process pasted image', 3000);
      } finally {
        // 恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingApi(false);
      }
    },
    [images, imageActions, handleRemoveBackground, showTips]
  );

  /**
   * 处理文件拖放事件
   */
  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: FileRejection[]) => {
      // 处理被拒绝的文件
      if (rejectedFiles.length > 0) {
        const fileTooLargeFiles = rejectedFiles.filter(rejection =>
          rejection.errors.some(error => error.code === 'file-too-large')
        );
        const invalidFormatFiles = rejectedFiles.filter(rejection =>
          rejection.errors.some(error => error.code === 'invalid-file-type')
        );

        if (fileTooLargeFiles.length > 0) {
          showTips('error', messages('batchEditor.fileSizeMaximum'), 3000);
        } else if (invalidFormatFiles.length > 0) {
          showTips(
            'error',
            messages('singleImage.unsupportedImageFormat'),
            3000
          );
        }
      }

      if (acceptedFiles.length === 0) {
        return;
      }

      // 检查图片数量限制
      const currentImageCount = images.length;
      const newFileCount = acceptedFiles.length;
      const totalCount = currentImageCount + newFileCount;

      if (totalCount > MAX_SINGLE_IMAGES_LIMIT) {
        showTips(
          'error',
          messages('singleImage.imagesExceedLimit', {
            count: MAX_SINGLE_IMAGES_LIMIT,
          }),
          4000
        );
        return;
      }

      // 双重验证：确保所有接受的文件都符合格式和大小要求
      const validFiles = acceptedFiles.filter(file => {
        const validation = validateImageFormat(file);
        if (!validation.isValid) {
          if (validation.error === 'file-too-large') {
            showTips('error', messages('batchEditor.fileSizeMaximum'), 3000);
          } else {
            showTips(
              'error',
              messages('singleImage.unsupportedImageFormat'),
              3000
            );
          }
        }
        return validation.isValid;
      });

      if (validFiles.length === 0) {
        return;
      }

      setIsLoadingApi(true);

      const processAllFiles = async () => {
        // 暂停历史记录跟踪，因为这是初始化过程
        useImageStore.temporal.getState().pause();

        for (let i = 0; i < validFiles.length; i++) {
          const file = validFiles[i];
          try {
            const newImage = await createImageFromSource(file);
            imageActions.addImage(newImage);

            // 第一张图片立即选中，以便显示上传状态
            if (i === 0) {
              imageActions.clearSelection();
              imageActions.toggleImageSelection(newImage.id);
            }

            // 只处理第一张图片，其他图片保持 original 状态
            if (i === 0) {
              await processImageBackgroundRemoval(
                newImage,
                handleRemoveBackground
              );
            } else {
              // 后续图片保持 original 状态，等待用户点击时处理
              console.log(
                `图片 ${file.name} 保持 original 状态，等待用户点击时处理`
              );
            }
          } catch (error) {
            console.error('Error processing file:', error);
            showTips('error', messages('singleImage.imageUploadFailed'), 3000);
          }
        }

        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingApi(false);
      };

      processAllFiles();
    },
    [images, imageActions, handleRemoveBackground, showTips]
  );

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/webp': ['.webp'],
    },
    multiple: true,
    disabled: isLoadingApi,
    noClick: true,
    validator: file => {
      const validation = validateImageFormat(file);
      if (!validation.isValid) {
        if (validation.error === 'file-too-large') {
          return {
            code: 'file-too-large',
            message: 'Image file is too large',
          };
        } else {
          return {
            code: 'invalid-file-type',
            message: 'Only JPG, JPEG, PNG, and WebP formats are supported',
          };
        }
      }
      return null;
    },
  });

  /**
   * 从URL加载图片
   */
  const handleLoadFromUrl = useCallback(
    async (url: string) => {
      if (!url.trim()) return;

      setIsLoadingUrl(true);
      setIsLoadingApi(true);

      // 暂停历史记录跟踪，因为这是初始化过程
      useImageStore.temporal.getState().pause();

      try {
        let blob: Blob;

        // 尝试多种方法获取图片
        try {
          // 方法1：直接fetch（适用于同源或支持CORS的图片）
          const response = await fetch(url, {
            mode: 'cors',
            headers: {
              Accept: 'image/*',
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          blob = await response.blob();
        } catch (corsError) {
          console.warn('直接fetch失败，使用服务端代理:', corsError);

          // 方法2：使用我们自己的服务端代理API
          try {
            const proxyResponse = await fetch(
              `/apps/api/proxy-image?url=${encodeURIComponent(url)}`
            );

            if (!proxyResponse.ok) {
              const errorData = await proxyResponse
                .json()
                .catch(() => ({ error: '代理服务器错误' }));
              throw new Error(errorData.error || '代理请求失败');
            }

            blob = await proxyResponse.blob();
          } catch (proxyError) {
            console.warn('服务端代理失败，尝试Canvas方式:', proxyError);

            // 方法3：使用Canvas和图片元素（最后的尝试）
            try {
              const img = new Image();
              img.crossOrigin = 'anonymous';

              await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = () => {
                  // 尝试不设置crossOrigin
                  const img2 = new Image();
                  img2.onload = resolve;
                  img2.onerror = reject;
                  img2.src = url;
                };
                img.src = url;
              });

              // 创建Canvas并转换为blob
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              if (!ctx) {
                throw new Error('无法创建Canvas上下文');
              }

              canvas.width = img.naturalWidth;
              canvas.height = img.naturalHeight;
              ctx.drawImage(img, 0, 0);

              blob = await new Promise<Blob>((resolve, reject) => {
                canvas.toBlob(result => {
                  if (result) {
                    resolve(result);
                  } else {
                    reject(new Error('Canvas转换失败'));
                  }
                }, 'image/png');
              });
            } catch (canvasError) {
              throw new Error(
                `无法加载图片：CORS限制或图片不存在。详情: ${canvasError}`
              );
            }
          }
        }

        // 验证blob类型
        if (!blob.type.startsWith('image/')) {
          // 尝试从URL推断图片类型
          const ext = url.split('.').pop()?.toLowerCase();
          const mimeTypes: { [key: string]: string } = {
            jpg: 'image/jpeg',
            jpeg: 'image/jpeg',
            png: 'image/png',
            webp: 'image/webp',
            gif: 'image/gif',
          };

          if (ext && mimeTypes[ext]) {
            blob = new Blob([blob], { type: mimeTypes[ext] });
          } else {
            throw new Error('无法识别的图片格式');
          }
        }

        const file = new File([blob], `url_image_${Date.now()}`, {
          type: blob.type,
        });

        const newImage = await createImageFromSource(file);
        imageActions.addImage(newImage);

        // 默认选中新添加的图片
        imageActions.clearSelection();
        imageActions.toggleImageSelection(newImage.id);

        // 将 Blob 转换为 Data URL 以发送到后端
        const dataUrl = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });

        // 调用 API 处理图片
        const resultUrl = await handleRemoveBackground(dataUrl, newImage.id);
        if (resultUrl) {
          imageActions.updateImage(newImage.id, {
            processedUrl: resultUrl,
            status: 'bg-removed',
          });
        }
      } catch (error) {
        console.error('从URL加载图片失败:', error);
        showTips('error', messages('singleImage.unableToOpenImage'));
      } finally {
        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingUrl(false);
        setIsLoadingApi(false);
      }
    },
    [imageActions, handleRemoveBackground, showTips, messages]
  );

  /**
   * 加载示例图片
   */
  const handleLoadSampleImage = useCallback(
    async (sampleUrl: string, sampleName: string, processedUrl?: string) => {
      setIsLoadingApi(true);

      // 暂停历史记录跟踪，因为这是初始化过程
      useImageStore.temporal.getState().pause();

      try {
        // 获取示例图片数据并创建 File 对象
        const response = await fetch(sampleUrl);
        if (!response.ok) {
          throw new Error(`无法获取示例图片: ${response.statusText}`);
        }
        const blob = await response.blob();
        const file = new File([blob], sampleName, { type: blob.type });

        const newImage = await createImageFromSource(file);
        imageActions.addImage(newImage);

        // 默认选中新添加的图片
        imageActions.clearSelection();
        imageActions.toggleImageSelection(newImage.id);

        let resultUrl: string | null = null;
        // 如果提供了预处理图片的URL，则获取并转换为blob URL
        if (processedUrl) {
          // 模拟API调用延迟，提供更好的用户体验, 同时展示加载动画
          await new Promise(resolve => setTimeout(resolve, 1500));

          // 获取处理后的图片并转换为blob URL
          try {
            const processedResponse = await fetch(processedUrl);
            if (processedResponse.ok) {
              const processedBlob = await processedResponse.blob();
              resultUrl = URL.createObjectURL(processedBlob);
            } else {
              console.warn(`无法获取处理后的图片: ${processedUrl}`);
              // 如果获取失败，降级为调用真实API
              const dataUrl = await new Promise<string>((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result as string);
                reader.onerror = reject;
                reader.readAsDataURL(file);
              });
              resultUrl = await handleRemoveBackground(dataUrl, newImage.id);
            }
          } catch (error) {
            console.warn('获取处理后的示例图片失败，降级为API调用:', error);
            // 如果获取失败，降级为调用真实API
            const dataUrl = await new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result as string);
              reader.onerror = reject;
              reader.readAsDataURL(file);
            });
            resultUrl = await handleRemoveBackground(dataUrl, newImage.id);
          }
        } else {
          // 否则，调用真实的API
          const dataUrl = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(file);
          });
          resultUrl = await handleRemoveBackground(dataUrl, newImage.id);
        }

        if (resultUrl) {
          imageActions.updateImage(newImage.id, {
            processedUrl: resultUrl,
            status: 'bg-removed',
          });
        } else {
          console.error(`处理示例图片 ${sampleName} 失败。`);
        }
      } catch (error) {
        console.error('加载示例图片失败:', error);
      } finally {
        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingApi(false);
      }
    },
    [imageActions, handleRemoveBackground, showTips]
  );

  /**
   * 粘贴事件监听器 - 支持图片文件和URL
   */
  useEffect(() => {
    const handlePaste = async (e: ClipboardEvent) => {
      // 只在没有焦点在输入框时处理粘贴
      const activeElement = document.activeElement;
      if (
        activeElement &&
        (activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.hasAttribute('contenteditable'))
      ) {
        return;
      }

      const items = e.clipboardData?.items;
      if (!items) return;

      // 首先尝试获取图片文件
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf('image') !== -1) {
          e.preventDefault();
          const file = item.getAsFile();
          if (file) {
            handlePasteFromClipboard(file);
          }
          return; // 找到图片就直接返回，不再处理URL
        }
      }

      // 如果没有图片，尝试获取文本（可能是URL）
      try {
        const text = await navigator.clipboard.readText();
        if (
          text &&
          (text.startsWith('http://') || text.startsWith('https://'))
        ) {
          // 简单检查是否可能是图片URL
          const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
          const lowerText = text.toLowerCase();
          const likelyImageUrl =
            imageExtensions.some(ext => lowerText.includes(ext)) ||
            lowerText.includes('image') ||
            lowerText.includes('photo') ||
            lowerText.includes('pic') ||
            text.match(/\.(jpg|jpeg|png|webp|gif)(\?|$)/i);

          if (likelyImageUrl) {
            e.preventDefault();
            // 直接调用URL加载功能
            await handleLoadFromUrl(text);
          }
        }
      } catch (clipboardError) {
        // 静默处理剪贴板读取错误
        console.debug('读取剪贴板文本失败:', clipboardError);
      }
    };

    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, [handlePasteFromClipboard, handleLoadFromUrl]);

  return {
    isLoadingApi,
    isLoadingUrl,
    isDragActive,
    getRootProps,
    getInputProps,
    open,
    handleLoadFromUrl,
    handleLoadSampleImage,
    handleRemoveBackground,
    processingImageIds,
    // 导出通用的背景去除函数
    processImageBackgroundRemoval: (image: {
      id: string;
      file?: File;
      previewUrl: string;
      status: string;
      processedUrl?: string | null;
    }) => processImageBackgroundRemoval(image, handleRemoveBackground),
  };
};

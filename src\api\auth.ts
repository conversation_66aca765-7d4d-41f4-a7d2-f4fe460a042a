import { httpClient } from './http-client';

// 认证相关的接口类型定义
export interface UserInfo {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  role?: string;
  permissions?: string[];
}

// 认证相关 API 函数

/**
 * 获取当前用户信息
 */
export async function getUserInfo(): Promise<UserInfo> {
  return httpClient.get<UserInfo>('/api/v1/user/info');
}

/**
 * 获取游客信息
 */
export async function getGuestInfo(): Promise<UserInfo> {
  return httpClient.get<UserInfo>('/api/v1/guest/info');
}

export interface HistoryData {
  type: string;
  way: string;
  remark: string;
  create_time: string;
  auth_type: string;
  num: number;
  time: string;
  date: string;
}

// 权益消耗相关的接口类型定义
export interface ConsumePermissionRequest {
  consumed_num?: number; // 消耗数量，非必须
  description?: string; // 描述，非必须。单个：Remove BG，批量：Batch Editor RemoveBG
}

export interface ConsumePermissionResponse {
  success: boolean;
  remaining_credits?: number; // 剩余积分
  message?: string;
}

interface GetHistoryDataResponse {
  list: HistoryData[];
  pager: {
    page: number;
    page_size: number;
    total: number;
  };
}

interface GetHistoryDataRequest {
  page: number;
  page_size: number;
  start_time: number;
  end_time: number;
}

/**
 * 获取历史数据
 */
export async function getHistoryData({
  page,
  page_size,
  start_time,
  end_time,
}: GetHistoryDataRequest): Promise<GetHistoryDataResponse> {
  return httpClient.get<GetHistoryDataResponse>('/api/v1/permission/history', {
    page,
    page_size,
    start_time,
    end_time,
  });
}

/**
 * 权益消耗接口
 *
 * 接口信息：
 * - 路径：POST /api/v1/permission/consume
 * - 参数：consumed_num (可选，消耗数量), description (可选，描述)
 * - 返回：{ code?: number, data?: any, msg?: string }
 *
 * @param request 权益消耗请求参数
 * @returns 权益消耗响应
 */
export async function consumePermission(
  request: ConsumePermissionRequest = {}
): Promise<ConsumePermissionResponse> {
  try {
    const response = await httpClient.post<ConsumePermissionResponse>(
      '/api/v1/permission/consume',
      request
    );
    return response;
  } catch (error) {
    console.error('权益消耗失败:', error);
    // 返回失败响应，但不抛出错误，避免影响主要功能
    return {
      success: false,
      message: error instanceof Error ? error.message : '权益消耗失败',
    };
  }
}
